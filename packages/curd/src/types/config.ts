import type { Ref } from 'vue'
import type { Curd<PERSON>pi, StdColumn } from './api'
import type { FormFieldType } from './form'
import type { CurdHooks } from './hooks'

// 按钮文本配置
export interface ButtonText {
  create?: string
  edit?: string
  delete?: string
  batchDelete?: string
  search?: string
  refresh?: string
  export?: string
  import?: string
}

// 字段选项配置
export interface FieldOption {
  label: string
  value: any
  disabled?: boolean
}

// 表格显示配置
export interface TableFieldConfig {
  /** 是否在表格中显示 */
  show?: boolean
  /** 自定义插槽名称 */
  slot?: string
  /** 列宽 */
  width?: number | string
  /** 是否可排序 */
  sortable?: boolean
  /** 是否固定列 */
  fixed?: 'left' | 'right'
  /** 自定义渲染函数 */
  render?: (value: any, record: any) => any
}

// 表单显示配置（重命名以避免冲突）
export interface CurdFormFieldConfig {
  /** 是否在表单中显示 */
  show?: boolean
  /** 是否必填 */
  required?: boolean
  /** 默认值 */
  default?: any
  /** 是否禁用 */
  disabled?: boolean | ((formData: Record<string, any>) => boolean)
  /** 验证规则 */
  rules?: Array<{
    required?: boolean
    message?: string
    min?: number
    max?: number
    pattern?: RegExp
    validator?: (value: any, formData: any) => boolean | string
  }>
  /** 列宽配置（1-12，对应 grid-cols） */
  col?: number
  /** 组件特定属性 */
  componentProps?: Record<string, any>
}

// 搜索配置
export interface SearchFieldConfig {
  /** 是否支持搜索 */
  show?: boolean
  /** 搜索类型，如果不指定则使用字段的 type */
  type?: FormFieldType
  /** 搜索占位符 */
  placeholder?: string
  /** 搜索组件特定属性 */
  componentProps?: Record<string, any>
}

// 统一字段配置（字段驱动的核心）
export interface CurdFieldConfig<T = any> {
  /** 字段键名，对应接口字段 */
  key: keyof T
  /** 字段中文名，用于显示 */
  label: string
  /** 渲染类型 */
  type: FormFieldType
  /** 下拉等类型的选项配置 */
  options?: ReadonlyArray<FieldOption>
  /** 占位符 */
  placeholder?: string

  /** 表格展示相关配置 */
  table?: TableFieldConfig | boolean
  /** 表单展示相关配置 */
  form?: CurdFormFieldConfig | boolean
  /** 搜索相关配置 */
  search?: SearchFieldConfig | boolean

  /** 权限标识 */
  permission?: string
  /** 字段描述/帮助文本 */
  description?: string
  /** 字段分组 */
  group?: string
}

// 操作按钮配置
export interface ActionsConfig {
  /** 是否显示新增按钮 */
  add?: boolean
  /** 是否显示编辑按钮 */
  edit?: boolean
  /** 是否显示删除按钮 */
  delete?: boolean
  /** 是否显示批量删除按钮 */
  batchDelete?: boolean
  /** 是否显示导出按钮 */
  export?: boolean
  /** 是否显示导入按钮 */
  import?: boolean
  /** 自定义操作按钮 */
  custom?: Array<{
    key: string
    label: string
    icon?: string
    type?: 'primary' | 'default' | 'danger'
    permission?: string
    handler: (record?: any, selectedRows?: any[]) => void
  }>
  /** 是否显示列按钮 */
  col?: boolean
}

// 新的统一 CRUD 配置（基于设计文档）
export interface CurdConfig<T = any> {
  /** 页面标题 */
  title: string
  /** API 接口 */
  api: CurdApi<T>
  /** 字段配置数组（核心驱动） */
  fields: CurdFieldConfig<T>[]

  /** 操作按钮配置 */
  actions?: ActionsConfig | boolean
  /** 是否启用分页 */
  pagination?: boolean
  /** 每页条数 */
  pageSize?: number
  /** 每页条数选项 */
  pageSizeOptions?: number[]

  /** 主键字段名 */
  primaryKey?: string
  /** 按钮文本配置 */
  btnText?: ButtonText

  /** 消息配置 */
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  successMessages?: {
    create?: string
    update?: string
    delete?: string
    batchDelete?: string
  }

  /** hooks 钩子函数 */
  hooks?: CurdHooks<T>

  /** 权限配置 */
  permissions?: {
    create?: string
    edit?: string
    delete?: string
    batchDelete?: string
    export?: string
    import?: string
  }
}

export interface UseCurdOptions<T = any> {
  api: CurdApi<T>
  columns: StdColumn<T>[]
  pageSize?: number
  pageSizeOptions?: number[]
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  hooks?: CurdHooks<T>
  immediate?: boolean
  primaryKey?: string
}

export interface UseCurdReturn<T = any> {
  data: Ref<T[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  total: Ref<number>
  currentPage: Ref<number>
  currentPageSize: Ref<number>
  totalPages: Ref<number>

  selectedRows: Ref<T[]>
  selectedRowKeys: Ref<(string | number)[]>

  searchParams: Ref<Record<string, any>>
  sortParams: Ref<Record<string, any>>

  refresh: () => Promise<void>
  search: (params: Record<string, any>) => Promise<void>
  sort: (field: string, order: 'asc' | 'desc') => Promise<void>
  changePage: (page: number) => Promise<void>
  changePageSize: (size: number) => Promise<void>
  create: (formData: Partial<T>) => Promise<T>
  update: (id: string | number, formData: Partial<T>) => Promise<T>
  remove: (id: string | number, permanently?: boolean) => Promise<void>
  batchRemove: (ids: (string | number)[], permanently?: boolean) => Promise<void>
  clearSelection: () => void
}
