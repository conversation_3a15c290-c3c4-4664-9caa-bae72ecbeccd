package auth

import (
	"context"
	"errors"
	"net/http"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/runtime_settings"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

type loginJson struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
}

const (
	ErrPasswordIncorrect     = 4031
	ErrMaxAttempts           = 4291
	ErrUserWaitForValidation = 4032
	ErrUserBlocked           = 4033
	ErrUserNotAllowed        = 4034
)

type LoginResponse struct {
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
	Token   string      `json:"token,omitempty"`
	User    *model.User `json:"user,omitempty"`
}

func Login(c *gin.Context) {
	ctx := context.Background()

	lock, err := redis.ObtainLock("login:"+c.RemoteIP()+c.GetHeader("X-Fingerprint"), 10*time.Millisecond, nil)

	if err != nil {
		c.JSON(http.StatusTooManyRequests, LoginResponse{
			Message: "Too many requests",
			Code:    http.StatusTooManyRequests,
		})
		return
	}
	defer lock.Release(ctx)

	var loginFailedKey = "login_failed:" + c.RemoteIP()

	auth := runtime_settings.GetAuthSettings()

	countStr, err := redis.Get(loginFailedKey)
	if err != nil {
		_ = redis.Set(loginFailedKey, 0,
			time.Duration(
				lo.Max([]int{auth.BanThresholdMinutes, 1}),
			)*time.Minute)
	}
	failedCount := cast.ToInt(countStr)

	if auth.MaxAttempts > 0 && failedCount >= auth.MaxAttempts {
		c.JSON(http.StatusNotAcceptable, LoginResponse{
			Message: "Max attempts exceeded",
			Code:    ErrMaxAttempts,
		})
		return
	}

	var login loginJson
	if !cosy.BindAndValid(c, &login) {
		_, _ = redis.Incr(loginFailedKey)
		return
	}

	u, err := user.Login(login.Email, login.Password)
	if err != nil {
		logger.Error(err)
		switch {
		case errors.Is(err, user.ErrPasswordIncorrect):
			c.JSON(http.StatusNotAcceptable, LoginResponse{
				Message: "Password incorrect",
				Code:    ErrPasswordIncorrect,
			})
		case errors.Is(err, user.ErrUserBlocked):
			c.JSON(http.StatusNotAcceptable, LoginResponse{
				Message: "The user is banned",
				Code:    ErrUserBlocked,
			})
		case errors.Is(err, user.ErrUserNotAllowed):
			c.JSON(http.StatusNotAcceptable, LoginResponse{
				Message: "The user is not allowed to login this system",
				Code:    ErrUserNotAllowed,
			})
		default:
			cosy.ErrHandler(c, err)
		}
		_, _ = redis.Incr(loginFailedKey)
		return
	}

	logger.Info("[User Login]", u.Name)

	u.UpdateLastActive()

	token, err := api.GenerateToken(u)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// api return
	c.JSON(http.StatusOK, LoginResponse{
		Message: "ok",
		Token:   token,
		User:    u,
	})
}

func Logout(c *gin.Context) {
	token := user.CurrentToken(c)
	_ = redis.Del(api.BuildTokenKey(token))
	c.JSON(http.StatusNoContent, nil)
}
