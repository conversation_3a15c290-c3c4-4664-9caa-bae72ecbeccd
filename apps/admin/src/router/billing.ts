import type { RouteRecordRaw } from 'vue-router'
import { DollarSign } from 'lucide-vue-next'

export const PATH_BILLING = '/billing'
export const PATH_BILLING_KEYS = `${PATH_BILLING}/keys`
export const PATH_BILLING_KEY_DETAIL = `${PATH_BILLING_KEYS}/:key`
export const PATH_BILLING_KEY_STATS = `${PATH_BILLING_KEY_DETAIL}/stats`
export const PATH_BILLING_PRICING = `${PATH_BILLING}/pricing`
export const PATH_BILLING_RECHARGE = `${PATH_BILLING}/recharge`
export const PATH_BILLING_QUOTA = `${PATH_BILLING}/quota`

export const billingRoutes: RouteRecordRaw[] = [
  {
    path: PATH_BILLING,
    redirect: PATH_BILLING_KEYS,
    meta: {
      title: '计费管理',
      requiresAuth: true,
      icon: DollarSign,
      showInMenu: true,
    },
    children: [
      {
        path: PATH_BILLING_KEYS,
        name: 'BillingK<PERSON><PERSON>',
        component: () => import('@/views/billing/keys/index.vue'),
        meta: {
          title: 'API Key',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_KEY_DETAIL,
        name: 'BillingKeyDetail',
        component: () => import('@/views/billing/keys/detail.vue'),
        meta: {
          title: '详情',
          requiresAuth: true,
        },
      },
      {
        path: PATH_BILLING_KEY_STATS,
        name: 'BillingKeyStats',
        component: () => import('@/views/billing/keys/stats.vue'),
        meta: {
          title: '用量统计',
          requiresAuth: true,
        },
      },
      {
        path: PATH_BILLING_PRICING,
        name: 'BillingPricing',
        component: () => import('@/views/billing/pricing/index.vue'),
        meta: {
          title: '计费规则',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_RECHARGE,
        name: 'BillingRecharge',
        component: () => import('@/views/billing/recharge/index.vue'),
        meta: {
          title: '充值管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_QUOTA,
        name: 'QuotaPackage',
        component: () => import('@/views/billing/quota/index.vue'),
        meta: {
          title: '资源包管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
]
