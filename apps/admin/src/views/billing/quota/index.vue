<script setup lang="ts">
import type { CurdConfig } from '@billing/curd'
import type { QuotaPackage } from '@/api/quota'
import { StdCurd } from '@billing/curd'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger, Avatar, AvatarFallback, AvatarImage, Badge, Card, CardContent, CardHeader, CardTitle, Progress, Switch, Tabs, TabsContent, TabsList, TabsTrigger } from '@billing/ui'
import { Activity, AlertTriangle, CheckCircle, Package, TrendingUp, XCircle } from 'lucide-vue-next'
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import { keyApi, searchUsers } from '@/api'
import { quotaApi } from '@/api/quota'
import { FORM_OPTIONS, MODULE_NAMES, QUOTA_STATUS, QUOTA_STATUS_TEXT, QUOTA_TYPE_LABELS, SERVICE_MODULE, SUCCESS_MESSAGES } from '@/constants'
import { getQuotaStatusVariant, getUsageRate } from '@/utils/business'
import { formatDate, formatNumber, formatTime, formatUsageWithUnit } from '@/utils/format'

// 状态定义
const activeTab = ref('list')

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  expired: 0,
  exhausted: 0,
  totalCost: 0, // 总成本（不同模块可以相加）
  totalRequests: 0, // 总请求数（不同模块可以相加）
  expiringSoon: 0,
})

const filteredConfig: CurdConfig<QuotaPackage> = {
  api: quotaApi,
  title: '资源包管理',
  primaryKey: 'id',

  fields: [
    {
      key: 'user_id',
      label: '关联用户',
      type: 'combobox',
      table: true,
      form: {
        required: true,
        rules: [
          { required: true, message: '请选择关联用户' },
        ],
        componentProps: {
          placeholder: '请输入用户名或邮箱搜索',
          remoteSearch: {
            searchApi: (query: string) => searchUsers(query),
            debounceMs: 300,
            minSearchLength: 1,
            showDefaultOptions: false,
            renderOption: (option: any) => {
              const parts = [option.label]
              if (option.email) {
                parts.push(`(${option.email})`)
              }
              return parts.join(' ')
            },
          },
        },
      },
      search: { placeholder: '请选择用户' },
    },
    {
      key: 'module',
      label: '服务类型',
      type: 'select',
      options: FORM_OPTIONS.SERVICE_MODULES as any,
      table: true,
      form: {
        required: true,
        rules: [
          { required: true, message: '服务类型是必填项' },
        ],
      },
      search: { placeholder: '请选择服务类型' },
    },
    {
      key: 'quota',
      label: '配额数量',
      type: 'number',
      table: true,
      form: {
        required: true,
        rules: [
          { required: true, message: '配额数量是必填项' },
          { min: 1, message: '配额数量必须大于0' },
        ],
      },
      search: { show: false },
    },
    {
      key: 'used',
      label: '已使用',
      type: 'number',
      table: true,
      form: false,
      search: false,
    },
    {
      key: 'api_key',
      label: 'API Key',
      type: 'select',
      table: true,
      form: {
        required: false,
        componentProps: {
          placeholder: '留空表示通用资源包',
          linkage: {
            dependsOn: ['user_id'],
            handler: async ({ user_id }: any) => {
              if (!user_id)
                return []
              const res = await keyApi.getList({ user_id })
              return res.data.map((item: any) => ({
                label: `${item.name}(${item.api_key})`,
                value: item.api_key,
              }))
            },
          },
        },
      },
      search: { placeholder: '请选择API Key' },
    },
    {
      key: 'model_name',
      label: '模型名称',
      type: 'text',
      placeholder: '留空表示通用模型',
      table: true,
      form: true,
      search: { placeholder: '请输入模型名称' },
    },
    {
      key: 'type',
      label: '资源包类型',
      type: 'select',
      options: [
        { label: '管理员赠送', value: 'admin' },
        { label: '购买获得', value: 'purchase' },
        { label: '促销活动', value: 'promotion' },
      ],
      table: true,
      form: { default: 'admin' },
      search: { placeholder: '请选择资源包类型' },
    },
    {
      key: 'expires_at',
      label: '过期时间',
      type: 'datetime',
      table: true,
      form: true,
      search: { type: 'date-range' },
    },
    {
      key: 'description',
      label: '描述',
      type: 'textarea',
      placeholder: '请输入资源包描述...',
      table: true,
      form: true,
      search: true,
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'datetime',
      table: true,
      search: { type: 'date-range' },
    },
  ],

  // 功能开关
  actions: {
    add: true,
    edit: true,
    delete: false,
    batchDelete: false,
  },

  // 消息配置
  successMessages: {
    create: SUCCESS_MESSAGES.QUOTA_CREATED,
    update: SUCCESS_MESSAGES.QUOTA_UPDATED,
  },
}

// 模块统计 - 按不同模块分开统计，不能简单相加因为单位不同
const moduleStats = ref([
  {
    module: 'llm',
    name: 'LLM服务',
    count: 0,
    total_usage: 0,
    quota: 0,
    unit: 'tokens',
    cost: 0,
  },
  {
    module: 'tts',
    name: 'TTS服务',
    count: 0,
    total_usage: 0,
    quota: 0,
    unit: 'characters',
    cost: 0,
  },
  {
    module: 'asr',
    name: 'ASR服务',
    count: 0,
    total_usage: 0,
    quota: 0,
    unit: 'seconds',
    cost: 0,
  },
])

// 获取统计数据
async function fetchStats() {
  try {
    const response = await quotaApi.getOverviewStats()
    stats.value = {
      total: response.total,
      active: response.active,
      expired: response.expired,
      exhausted: response.exhausted,
      totalCost: response.total_cost,
      totalRequests: response.module_stats.reduce((sum, stat) => sum + stat.count, 0), // 总请求数为各模块资源包数量之和
      expiringSoon: response.expiring_soon,
    }

    // 更新模块统计数据
    moduleStats.value = response.module_stats.map(stat => ({
      module: stat.module,
      name: stat.name,
      count: stat.count,
      total_usage: stat.total_used,
      quota: stat.total_quota,
      unit: stat.unit,
      cost: stat.cost,
    }))
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    stats.value = {
      total: 0,
      active: 0,
      expired: 0,
      exhausted: 0,
      totalCost: 0,
      totalRequests: 0,
      expiringSoon: 0,
    }
    moduleStats.value = []
  }
}

// 获取状态颜色 - 使用公共函数
const getStatusVariant = getQuotaStatusVariant

// 获取状态文本 - 使用公共常量
function getStatusText(status: string) {
  return QUOTA_STATUS_TEXT[status as keyof typeof QUOTA_STATUS_TEXT] || status
}

// 计算使用率 - 使用公共函数
const getUsagePercentage = getUsageRate

// 启用/禁用资源包
async function toggleQuotaStatus(row: QuotaPackage) {
  try {
    if (row.status === QUOTA_STATUS.ACTIVE) {
      await quotaApi.disableItem(row.id)
    }
    else {
      await quotaApi.enableItem(row.id)
    }
    await fetchStats()
  }
  catch (error) {
    toast.error('启用/禁用资源包失败')
  }
}

// 初始化
onMounted(() => {
  fetchStats()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          资源包管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理用户资源包，包括LLM、TTS、ASR等服务的配额分配。支持为指定Key或全局分配。
        </p>
      </div>
    </div>

    <!-- 标签页导航 -->
    <Tabs
      v-model="activeTab"
      class="w-full"
    >
      <TabsList class="grid w-full grid-cols-3 mb-2">
        <TabsTrigger value="overview">
          概览
        </TabsTrigger>
        <TabsTrigger value="list">
          资源包列表
        </TabsTrigger>
        <TabsTrigger value="analytics">
          统计分析
        </TabsTrigger>
      </TabsList>

      <!-- 概览标签页 -->
      <TabsContent
        value="overview"
        class="space-y-6"
      >
        <!-- 总体统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总资源包
              </CardTitle>
              <Package class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.total }}
              </div>
              <p class="text-xs text-muted-foreground">
                活跃: {{ stats.active }}，过期: {{ stats.expired }}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总成本
              </CardTitle>
              <TrendingUp class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                ¥{{ stats.totalCost.toFixed(2) }}
              </div>
              <p class="text-xs text-muted-foreground">
                所有模块用量总成本
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总请求数
              </CardTitle>
              <Activity class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.totalRequests.toLocaleString() }}
              </div>
              <p class="text-xs text-muted-foreground">
                所有模块总请求次数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                即将过期
              </CardTitle>
              <AlertTriangle class="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-yellow-600">
                {{ stats.expiringSoon }}
              </div>
              <p class="text-xs text-muted-foreground">
                7天内过期
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 按模块统计 -->
        <Card>
          <CardHeader>
            <CardTitle>按服务类型统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="flex items-center justify-between p-4 border rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <Badge
                    variant="outline"
                    :class="{
                      'bg-blue-100 text-blue-800': module.module === SERVICE_MODULE.LLM,
                      'bg-green-100 text-green-800': module.module === SERVICE_MODULE.TTS,
                      'bg-purple-100 text-purple-800': module.module === SERVICE_MODULE.ASR,
                    }"
                  >
                    {{ module.name }}
                  </Badge>
                  <div class="text-sm text-gray-600">
                    {{ module.count }} 个资源包
                  </div>
                </div>
                <div class="text-right">
                  <div class="font-medium">
                    {{ formatUsageWithUnit(module.total_usage, module.unit) }} / {{ formatUsageWithUnit(module.quota, module.unit) }}
                  </div>
                  <div class="text-sm text-gray-500">
                    使用率: {{ module.quota > 0 ? Math.round((module.total_usage / module.quota) * 100) : 0 }}%
                  </div>
                  <div class="text-xs text-gray-400">
                    成本: ¥{{ module.cost.toFixed(2) }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 资源包列表标签页 -->
      <TabsContent
        value="list"
        class="space-y-6"
      >
        <!-- 使用 StdCurd 组件 -->
        <StdCurd :config="filteredConfig">
          <!-- 自定义用户列渲染 -->
          <template #cell-user_id="{ row }">
            <div
              v-if="row.user"
              class="flex items-center"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage :src="row.user.avatar" />
                <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
              </Avatar>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ row.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ row.user.email }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              用户信息缺失
            </div>
          </template>

          <!-- 自定义模块列渲染 -->
          <template #cell-module="{ value }">
            <Badge
              variant="outline"
              :class="{
                'bg-blue-100 text-blue-800': value === SERVICE_MODULE.LLM,
                'bg-green-100 text-green-800': value === SERVICE_MODULE.TTS,
                'bg-purple-100 text-purple-800': value === SERVICE_MODULE.ASR,
              }"
            >
              {{ MODULE_NAMES[value] }}
            </Badge>
          </template>

          <!-- 自定义配额使用列渲染 -->
          <template #cell-quota="{ row }">
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>{{ formatNumber(row.used) }} / {{ formatNumber(row.quota) }}</span>
                <span class="text-gray-500">{{ getUsagePercentage(row.used, row.quota) }}%</span>
              </div>
              <Progress
                :value="getUsagePercentage(row.used, row.quota)"
                class="h-2"
              />
              <div class="text-xs text-gray-500">
                剩余: {{ formatNumber(row.available) }}
              </div>
            </div>
          </template>

          <!-- 自定义API Key列渲染 -->
          <template #cell-api_key="{ value }">
            <div class="text-sm font-mono text-gray-600">
              {{ value ? `${value.substring(0, 4)}********${value.substring(value.length - 4)}` : '通用' }}
            </div>
          </template>

          <!-- 自定义模型列渲染 -->
          <template #cell-model_name="{ value }">
            <div class="text-sm text-gray-600">
              {{ value || '通用' }}
            </div>
          </template>

          <!-- 自定义过期时间列渲染 -->
          <template #cell-expires_at="{ value }">
            <div class="text-sm text-gray-500">
              {{ value ? formatDate(value) : '永不过期' }}
            </div>
          </template>

          <!-- 自定义类型列渲染 -->
          <template #cell-type="{ value }">
            <Badge variant="outline">
              {{ QUOTA_TYPE_LABELS[value] }}
            </Badge>
          </template>

          <!-- 自定义状态列渲染 -->
          <template #cell-status="{ value }">
            <Badge :variant="getStatusVariant(value)">
              <CheckCircle
                v-if="value === QUOTA_STATUS.ACTIVE"
                class="w-3 h-3 mr-1"
              />
              <XCircle
                v-else-if="value === QUOTA_STATUS.DISABLED"
                class="w-3 h-3 mr-1"
              />
              {{ getStatusText(value) }}
            </Badge>
          </template>

          <template #cell-created_at="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatTime(value) }}
            </div>
          </template>

          <!-- 自定义操作列 -->
          <template #actions="{ row }">
            <AlertDialog>
              <AlertDialogTrigger class="flex items-center">
                <Switch :model-value="row.status === QUOTA_STATUS.ACTIVE" />
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {{ row.status === QUOTA_STATUS.ACTIVE ? '禁用' : '启用' }}
                  </AlertDialogTitle>
                </AlertDialogHeader>
                <AlertDialogDescription>
                  {{ row.status === QUOTA_STATUS.ACTIVE ? '确定要禁用该资源包吗？' : '确定要启用该资源包吗？' }}
                </AlertDialogDescription>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    class="bg-destructive hover:bg-destructive/80"
                    @click="toggleQuotaStatus(row)"
                  >
                    确定
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </template>

          <!-- 自定义创建时间列渲染 -->
          <template #cell-created-at="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatDate(value) }}
            </div>
          </template>
        </StdCurd>
      </TabsContent>

      <!-- 统计分析标签页 -->
      <TabsContent
        value="analytics"
        class="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>统计分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-center py-8 text-gray-500">
              <TrendingUp class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>统计分析功能开发中...</p>
              <p class="text-sm">
                将提供用量趋势、用户分析等功能
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
