<script setup lang="ts">
import type { CurdConfig } from '@billing/curd'
import type { User } from '@/api'
import { StdCurd } from '@billing/curd'
import { Avatar, AvatarFallback, AvatarImage, Badge, Button } from '@billing/ui'
import { Eye, Phone } from 'lucide-vue-next'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { userApi } from '@/api'
import {
  CONFIRM_MESSAGES,
  FORM_OPTIONS,
  PAGINATION,
  SUCCESS_MESSAGES,
  USER_STATUS,
} from '@/constants'
import { formatCurrency, formatDate, formatTimeAgo } from '@/utils'

const router = useRouter()

const curdRef = ref<typeof StdCurd>()

const systemUserApi = {
  ...userApi,
  getList: async (params: any) => {
    return await userApi.getList({
      ...params,
      user_type: 'admin',
    })
  },
}

// StdCurd 配置
const config: CurdConfig<User> = {
  api: systemUserApi,
  title: '用户管理',
  fields: [
    {
      key: 'name',
      label: '用户名称',
      type: 'text',
      placeholder: '请输入用户名称',
      table: true,
      form: {
        required: true,
        rules: [
          { required: true, message: '用户名称是必填项' },
          { min: 2, max: 20, message: '用户名称长度应在2-20个字符之间' },
        ],
      },
      search: { placeholder: '请输入用户名称搜索' },
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      table: false, // 在name列中显示
      form: {
        required: true,
        rules: [
          { required: true, message: '邮箱是必填项' },
        ],
      },
      search: { placeholder: '请输入邮箱搜索' },
    },
    {
      key: 'password',
      label: '密码',
      type: 'password',
      table: false,
      form: {
        required: true,
        rules: [
          { required: true, message: '密码是必填项' },
          { min: 6, message: '密码长度不能小于6位' },
        ],
      },
    },
    {
      key: 'phone',
      label: '手机号',
      type: 'text',
      placeholder: '请输入手机号',
      form: true,
      search: { placeholder: '请输入手机号搜索' },
    },
    {
      key: 'balance',
      label: '余额',
      type: 'number',
      table: true,
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      options: FORM_OPTIONS.USER_STATUS,
      table: true,
      form: {
        default: 1,
      },
      search: { placeholder: '请选择状态' },
    },
    {
      key: 'last_active',
      label: '最近活跃',
      type: 'datetime',
      table: true,
      search: { type: 'date-range' },
    },
    {
      key: 'created_at',
      label: '注册时间',
      type: 'datetime',
      table: true,
      search: { type: 'date-range' },
    },
  ],

  // 基础配置
  primaryKey: 'id',
  pageSize: PAGINATION.DEFAULT_PAGE_SIZE,

  // 功能开关
  actions: {
    add: true,
    edit: false,
    delete: true,
    batchDelete: true,
  },

  // 消息配置
  confirmMessages: {
    delete: CONFIRM_MESSAGES.DELETE_USER,
    batchDelete: CONFIRM_MESSAGES.BATCH_DELETE_USERS,
  },

  successMessages: {
    create: SUCCESS_MESSAGES.USER_CREATED,
    update: SUCCESS_MESSAGES.USER_UPDATED,
    delete: SUCCESS_MESSAGES.USER_DELETED,
    batchDelete: SUCCESS_MESSAGES.USER_BATCH_DELETED,
  },
}

// 查看用户详情
function viewUser(user: User) {
  router.push(`/users/${user.id}`)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          用户管理
        </h1>
      </div>
    </div>

    <!-- 使用 StdCurd 组件 -->
    <StdCurd
      ref="curdRef"
      :config="config"
    >
      <!-- 自定义用户信息列渲染 -->
      <template #cell-name="{ value, row }">
        <div class="flex items-center">
          <Avatar class="h-10 w-10">
            <AvatarImage :src="row.avatar" />
            <AvatarFallback>{{ (value || '').charAt(0).toUpperCase() }}</AvatarFallback>
          </Avatar>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">
              {{ value }}
            </div>
            <div class="text-sm text-gray-500">
              {{ row.email }}
            </div>
            <div
              v-if="row.phone"
              class="text-xs text-gray-400 flex items-center gap-1"
            >
              <Phone class="w-3 h-3" />
              {{ row.phone }}
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义状态列渲染 -->
      <template #cell-status="{ value }">
        <Badge :variant="value === USER_STATUS.ACTIVE ? 'default' : 'secondary'">
          <div class="flex items-center gap-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="value === USER_STATUS.ACTIVE ? 'bg-green-500' : 'bg-gray-400'"
            />
            {{ value === USER_STATUS.ACTIVE ? '激活' : '禁用' }}
          </div>
        </Badge>
      </template>

      <!-- 自定义余额列渲染 -->
      <template #cell-balance="{ value }">
        <div class="text-sm font-medium">
          <span :class="value > 0 ? 'text-green-600' : 'text-gray-400'">
            {{ formatCurrency(value || 0) }}
          </span>
        </div>
      </template>

      <!-- 自定义最近活跃列渲染 -->
      <template #cell-last_active="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatTimeAgo(value) }}
        </div>
      </template>

      <!-- 自定义注册时间列渲染 -->
      <template #cell-created_at="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatDate(value) }}
        </div>
      </template>

      <!-- 自定义操作列 -->
      <template #before-actions="{ row }">
        <div class="flex items-center justify-end gap-1">
          <Button
            variant="ghost"
            size="sm"
            @click="viewUser(row)"
          >
            <Eye class="w-4 h-4" />
          </Button>
        </div>
      </template>
    </StdCurd>
  </div>
</template>
