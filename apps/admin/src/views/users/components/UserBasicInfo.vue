<script setup lang="ts">
import type { UploadFile, User } from '@/api'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@billing/ui'
import {
  Check,
  Edit,
  Loader2,
  Upload,
  User as UserIcon,
  X,
} from 'lucide-vue-next'
import { reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { uploadApi, userApi } from '@/api'
import { USER_STATUS, USER_TYPE } from '@/constants'

interface Props {
  user: User | null
}

interface Emits {
  (e: 'update:user', value: User): void
  (e: 'saveSuccess'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadingAvatar = ref(false)
const fileInput = ref<HTMLInputElement>()
const isEditMode = ref(false)
const saving = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  email: '',
  phone: '',
  avatar: '',
  status: USER_STATUS.ACTIVE,
  user_type: USER_TYPE.CLIENT,
})

// 监听用户数据变化，更新表单
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      Object.assign(formData, {
        name: newUser.name || '',
        email: newUser.email || '',
        phone: newUser.phone || '',
        avatar: newUser.avatar || '',
        status: newUser.status ?? USER_STATUS.ACTIVE,
        user_type: newUser.user_type ?? USER_TYPE.CLIENT,
      })
    }
  },
  { immediate: true },
)

// 检查表单有效性
function isFormValid() {
  return formData.name.trim() && formData.email.trim()
}

// 切换编辑模式
function toggleEditMode() {
  isEditMode.value = true
}

// 取消编辑
function cancelEdit() {
  isEditMode.value = false
  resetForm()
}

// 触发文件上传
function triggerFileUpload() {
  fileInput.value?.click()
}

// 处理文件上传
async function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file)
    return

  // 验证文件类型和大小
  if (!file.type.startsWith('image/')) {
    toast.error('文件类型错误', {
      description: '请选择图片文件',
    })
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    toast.error('文件过大', {
      description: '文件大小不能超过5MB',
    })
    return
  }

  if (!props.user?.id)
    return

  uploadingAvatar.value = true

  try {
    // 上传头像到服务器
    const uploadResult: UploadFile = await uploadApi.upload(file, 'avatar')

    // 更新用户头像
    await userApi.updateAvatar(props.user.id, uploadResult.id)

    // 更新本地显示
    formData.avatar = uploadResult.path

    // 通知父组件刷新数据
    emit('saveSuccess')

    toast.success('头像上传成功', {
      description: '用户头像已更新',
    })
  }
  catch (error: any) {
    console.error('上传头像失败:', error)
    toast.error('上传失败', {
      description: error?.message || '请稍后重试',
    })
  }
  finally {
    uploadingAvatar.value = false
    // 清空文件输入
    if (target)
      target.value = ''
  }
}

// 保存更改
async function saveChanges() {
  if (!isFormValid()) {
    toast.error('表单验证失败', {
      description: '姓名和邮箱为必填项',
    })
    return
  }

  if (!props.user?.id)
    return

  saving.value = true

  try {
    await userApi.updateItem(props.user.id, formData)

    isEditMode.value = false
    emit('saveSuccess')

    toast.success('保存成功', {
      description: '用户信息已更新',
    })
  }
  catch (error: any) {
    console.error('保存失败:', error)
    toast.error('保存失败', {
      description: error?.message || '请稍后重试',
    })
  }
  finally {
    saving.value = false
  }
}

// 重置表单数据
function resetForm() {
  if (props.user) {
    Object.assign(formData, {
      name: props.user.name || '',
      email: props.user.email || '',
      phone: props.user.phone || '',
      avatar: props.user.avatar || '',
      status: props.user.status ?? USER_STATUS.ACTIVE,
      user_type: props.user.user_type ?? USER_TYPE.CLIENT,
    })
  }
}

defineExpose({
  saveChanges,
  resetForm,
  isFormValid,
})
</script>

<template>
  <Card>
    <CardHeader>
      <div class="flex items-center justify-between">
        <CardTitle class="flex items-center gap-2">
          <UserIcon class="w-5 h-5" />
          基本信息
        </CardTitle>
        <div class="flex items-center gap-2">
          <Button
            v-if="!isEditMode"
            variant="outline"
            size="sm"
            @click="toggleEditMode"
          >
            <Edit class="w-4 h-4" />
            编辑
          </Button>
          <template v-else>
            <Button
              variant="outline"
              size="sm"
              :disabled="saving"
              @click="cancelEdit"
            >
              <X class="w-4 h-4" />
              取消
            </Button>
            <Button
              size="sm"
              :disabled="!isFormValid() || saving"
              @click="saveChanges"
            >
              <Loader2
                v-if="saving"
                class="w-4 h-4 animate-spin"
              />
              <Check
                v-else
                class="w-4 h-4"
              />
              保存
            </Button>
          </template>
        </div>
      </div>
    </CardHeader>
    <CardContent class="space-y-6">
      <!-- 头像上传 -->
      <div class="flex items-center gap-6">
        <div class="relative">
          <Avatar class="h-24 w-24">
            <AvatarImage :src="formData.avatar" />
            <AvatarFallback class="text-2xl">
              {{ (formData.name || '').charAt(0).toUpperCase() }}
            </AvatarFallback>
          </Avatar>
          <div
            v-if="uploadingAvatar"
            class="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center"
          >
            <Loader2 class="w-6 h-6 text-white animate-spin" />
          </div>
        </div>

        <div
          v-if="isEditMode"
          class="space-y-2"
        >
          <Button
            variant="outline"
            :disabled="uploadingAvatar"
            @click="triggerFileUpload"
          >
            <Upload class="w-4 h-4" />
            {{ uploadingAvatar ? '上传中...' : '上传头像' }}
          </Button>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleFileUpload"
          >
          <p class="text-xs text-gray-500">
            支持 JPG、PNG、WebP 格式，最大 5MB
          </p>
        </div>
      </div>

      <!-- 表单字段 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-2">
          <Label for="name">姓名 <span class="text-red-500">*</span></Label>
          <Input
            id="name"
            v-model="formData.name"
            :disabled="!isEditMode"
            placeholder="请输入姓名"
            :class="{ 'border-red-500': isEditMode && !formData.name.trim() }"
          />
        </div>

        <div class="space-y-2">
          <Label for="email">邮箱 <span class="text-red-500">*</span></Label>
          <Input
            id="email"
            v-model="formData.email"
            type="email"
            :disabled="!isEditMode"
            placeholder="请输入邮箱地址"
            :class="{ 'border-red-500': isEditMode && !formData.email.trim() }"
          />
        </div>

        <div class="space-y-2">
          <Label for="phone">手机号</Label>
          <Input
            id="phone"
            v-model="formData.phone"
            :disabled="!isEditMode"
            placeholder="请输入手机号"
          />
        </div>

        <div class="space-y-2">
          <Label for="status">状态</Label>
          <Select
            v-model="formData.status"
            :disabled="!isEditMode"
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem :value="USER_STATUS.ACTIVE">
                激活
              </SelectItem>
              <SelectItem :value="USER_STATUS.BLOCKED">
                禁用
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="space-y-2">
          <Label for="user_type">用户类型</Label>
          <Select
            v-model="formData.user_type"
            :disabled="!isEditMode"
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem :value="USER_TYPE.ADMIN">
                管理员
              </SelectItem>
              <SelectItem :value="USER_TYPE.CLIENT">
                客户
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
