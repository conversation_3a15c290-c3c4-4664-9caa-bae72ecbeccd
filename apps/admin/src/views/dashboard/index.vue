<script setup lang="ts">
import type { DashboardData, DashboardStats } from '@/api'
import {
  Alert,
  AlertDescription,
  Avatar,
  AvatarFallback,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Progress,
} from '@billing/ui'
import {
  AlertTriangle,
  ArrowUpRight,
  CreditCard,
  DollarSign,
  Key,
  Package,
  PieChart,
  RefreshCcw,
  TrendingUp,
  Users,
  Wallet,
} from 'lucide-vue-next'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { dashboardApi } from '@/api'
import { RECHARGE_TYPE_LABELS, TIME_INTERVALS } from '@/constants'
import { PATH_BILLING_KEYS, PATH_BILLING_QUOTA, PATH_BILLING_RECHARGE, PATH_USERS_CLIENT } from '@/router'
import {
  formatCurrency,
  formatNumber,
  getRechargeTypeColor,
  getTrendColor,
  getTrendIcon,
} from '@/utils'

const router = useRouter()

// 统计数据
const stats = ref<DashboardStats>({
  // 核心指标
  total_revenue: 0,
  month_revenue: 0,
  today_revenue: 0,
  total_users: 0,
  active_users: 0,
  total_keys: 0,
  active_keys: 0,
  blocked_keys: 0,

  // 计费相关
  total_quota_packages: 0,
  active_quota_packages: 0,
  total_balance: 0,
  avg_user_balance: 0,

  // 使用量统计
  total_usage: 0,
  month_usage: 0,
  today_usage: 0,
  avg_daily_usage: 0,

  // 趋势数据
  revenue_growth: 0,
  user_growth: 0,
  usage_growth: 0,
  balance_growth: 0,

  // 模块统计
  module_stats: [],
})

// 模块使用统计 - 现在从API获取
const moduleStats = computed(() => stats.value.module_stats || [])

// Dashboard完整数据
const dashboardData = ref<DashboardData | null>(null)

// 近期活跃用户
const recentActiveUsers = computed(() => dashboardData.value?.recent_active_users || [])

// 近期充值记录
const recentRecharges = computed(() => dashboardData.value?.recent_recharges || [])

// 加载状态
const loading = ref(false)
const error = ref<string | null>(null)

// 自动刷新
const autoRefresh = ref(true)
const refreshInterval = ref<number | null>(null)

// 启动自动刷新
function startAutoRefresh() {
  if (autoRefresh.value && !refreshInterval.value) {
    refreshInterval.value = setInterval(() => {
      if (!loading.value) {
        fetchDashboardData()
      }
    }, TIME_INTERVALS.AUTO_REFRESH)
  }
}

// 停止自动刷新
function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 导航到详细页面
function navigateTo(path: string) {
  router.push(path)
}

// 获取统计数据
async function fetchDashboardStats() {
  try {
    loading.value = true
    error.value = null

    const response = await dashboardApi.getStats()
    stats.value = response

    console.log('Dashboard统计数据获取成功')
  }
  catch (err) {
    console.error('获取Dashboard统计数据失败:', err)
    error.value = '获取统计数据失败'
  }
  finally {
    loading.value = false
  }
}

// 获取完整Dashboard数据
async function fetchDashboardData() {
  try {
    loading.value = true
    error.value = null

    const response = await dashboardApi.getData()
    dashboardData.value = response
    stats.value = response.stats

    console.log('Dashboard完整数据获取成功')
  }
  catch (err) {
    console.error('获取Dashboard完整数据失败:', err)
    error.value = '获取Dashboard数据失败'

    // 如果完整数据获取失败，尝试只获取统计数据
    await fetchDashboardStats()
  }
  finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchDashboardData()
  startAutoRefresh()
})

// 清理
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          计费系统概览
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          监控系统运行状态、用户活动、收入情况和资源使用情况。
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="loading"
          @click="fetchDashboardData"
        >
          <RefreshCcw
            class="w-4 h-4"
            :class="{ 'animate-spin': loading }"
          />
        </Button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="flex items-center justify-center py-8"
    >
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2" />
        <p class="text-sm text-muted-foreground">
          加载中...
        </p>
      </div>
    </div>

    <!-- 错误状态 -->
    <Alert
      v-if="error && !loading"
      variant="destructive"
      class="mb-6"
    >
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription>
        {{ error }}
        <Button
          variant="outline"
          size="sm"
          class="ml-2"
          @click="fetchDashboardData"
        >
          重试
        </Button>
      </AlertDescription>
    </Alert>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- 总收入 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            总收入
          </CardTitle>
          <DollarSign class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatCurrency(stats.total_revenue) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.revenue_growth)"
              :class="`w-3 h-3 ${getTrendColor(stats.revenue_growth)}`"
            />
            <span :class="getTrendColor(stats.revenue_growth)">{{ stats.revenue_growth > 0 ? '+' : '' }}{{ stats.revenue_growth.toFixed(2) }}%</span>
            <span>与上月对比</span>
          </div>
        </CardContent>
      </Card>

      <!-- 活跃用户 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            活跃用户
          </CardTitle>
          <Users class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatNumber(stats.active_users) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.user_growth)"
              :class="`w-3 h-3 ${getTrendColor(stats.user_growth)}`"
            />
            <span :class="getTrendColor(stats.user_growth)">{{ stats.user_growth > 0 ? '+' : '' }}{{ stats.user_growth }}%</span>
            <span>用户增长</span>
          </div>
        </CardContent>
      </Card>

      <!-- API Keys -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            API Keys
          </CardTitle>
          <Key class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatNumber(stats.active_keys) }}
          </div>
          <div class="text-xs text-muted-foreground">
            总计 {{ formatNumber(stats.total_keys) }}，阻止 {{ formatNumber(stats.blocked_keys) }}
          </div>
        </CardContent>
      </Card>

      <!-- 用户余额 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            用户余额
          </CardTitle>
          <Wallet class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatCurrency(stats.total_balance, 'CNY', { maximumFractionDigits: 2 }) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.balance_growth)"
              :class="`w-3 h-3 ${getTrendColor(stats.balance_growth)}`"
            />
            <span :class="getTrendColor(stats.balance_growth)">{{ stats.balance_growth > 0 ? '+' : '' }}{{ stats.balance_growth.toFixed(2) }}%</span>
            <span>余额增长</span>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：服务使用统计 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 按服务类型统计 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <PieChart class="w-5 h-5" />
                服务使用统计
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo(PATH_BILLING_KEYS)"
              >
                查看详情
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="space-y-3"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <div
                      class="w-3 h-3 rounded-full"
                      :class="{
                        'bg-blue-500': module.module === 'llm',
                        'bg-green-500': module.module === 'tts',
                        'bg-purple-500': module.module === 'asr',
                      }"
                    />
                    <div>
                      <div class="font-medium">
                        {{ module.module.toUpperCase() }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ module.keys }} 个Key使用
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col items-end text-right">
                    <div class="font-medium">
                      {{ formatCurrency(module.revenue) }}
                    </div>
                    <div class="flex items-center gap-1 text-xs">
                      <component
                        :is="getTrendIcon(module.growth)"
                        :class="`w-3 h-3 ${getTrendColor(module.growth)}`"
                      />
                      <span :class="getTrendColor(module.growth)">{{ module.growth > 0 ? '+' : '' }}{{ module.growth.toFixed(2) }}%</span>
                    </div>
                  </div>
                </div>
                <div class="space-y-1">
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>使用量: {{ formatNumber(module.usage) }}</span>
                    <span>{{ stats.total_usage > 0 ? Math.round((module.usage / stats.total_usage) * 100) : 0 }}%</span>
                  </div>
                  <Progress
                    :model-value="stats.total_usage > 0 ? (module.usage / stats.total_usage) * 100 : 0"
                    class="h-2"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 今日收入分析 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <TrendingUp class="w-5 h-5" />
                今日收入分析
              </CardTitle>
              <Badge variant="outline">
                {{ formatCurrency(stats.today_revenue) }}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-lg font-bold text-green-600">
                  {{ formatCurrency(stats.today_revenue * 0.6) }}
                </div>
                <div class="text-xs text-green-600">
                  LLM服务
                </div>
              </div>
              <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-lg font-bold text-blue-600">
                  {{ formatCurrency(stats.today_revenue * 0.25) }}
                </div>
                <div class="text-xs text-blue-600">
                  TTS服务
                </div>
              </div>
              <div class="text-center p-3 bg-purple-50 rounded-lg">
                <div class="text-lg font-bold text-purple-600">
                  {{ formatCurrency(stats.today_revenue * 0.15) }}
                </div>
                <div class="text-xs text-purple-600">
                  ASR服务
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：实时活动 -->
      <div class="space-y-6">
        <!-- 近期活跃用户 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <Users class="w-5 h-5" />
                近期活跃用户
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo(PATH_USERS_CLIENT)"
              >
                查看全部
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div
                v-for="user in recentActiveUsers"
                :key="user.id"
                class="flex items-center justify-between"
              >
                <div class="flex items-center gap-3">
                  <Avatar class="h-8 w-8">
                    <AvatarFallback>{{ user.name.charAt(0) }}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div class="text-sm font-medium">
                      {{ user.name }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ user.last_active }}
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-xs font-medium">
                    {{ formatCurrency(user.balance) }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ formatNumber(user.usage) }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 近期充值记录 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <CreditCard class="w-5 h-5" />
                近期充值
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo(PATH_BILLING_RECHARGE)"
              >
                查看全部
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div
                v-for="recharge in recentRecharges"
                :key="recharge.id"
                class="flex items-center justify-between"
              >
                <div>
                  <div class="text-sm font-medium">
                    {{ recharge.user }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ recharge.time }}
                  </div>
                </div>
                <div class="flex flex-col items-end gap-1 text-right">
                  <div class="text-sm font-medium text-green-600">
                    +{{ formatCurrency(recharge.amount) }}
                  </div>
                  <div :class="`w-fit text-xs px-2 py-1 rounded-full ${getRechargeTypeColor(recharge.type)}`">
                    {{ RECHARGE_TYPE_LABELS[recharge.type] }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 快速操作 -->
    <Card>
      <CardHeader>
        <CardTitle>快速操作</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo(PATH_BILLING_KEYS)"
          >
            <Key class="w-6 h-6 mb-2" />
            <span>管理API Key</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo(PATH_BILLING_QUOTA)"
          >
            <Package class="w-6 h-6 mb-2" />
            <span>资源包管理</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo(PATH_BILLING_RECHARGE)"
          >
            <CreditCard class="w-6 h-6 mb-2" />
            <span>充值管理</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo(PATH_USERS_CLIENT)"
          >
            <Users class="w-6 h-6 mb-2" />
            <span>客户管理</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 300ms linear ease-in-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
